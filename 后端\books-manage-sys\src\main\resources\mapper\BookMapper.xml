<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.kmbeast.mapper.BookMapper">

    <insert id="save">
        INSERT INTO book (name, cover, publisher, author, isbn, num, detail, bookshelf_id, category_id, is_plan_buy,
                          plan_buy_time,
                          create_time)
        VALUES (#{name}, #{cover}, #{publisher}, #{author}, #{isbn}, #{num}, #{detail}, #{bookShelfId}, #{categoryId},
                #{isPlanBuy},
                #{planBuyTime}, #{createTime})
    </insert>

    <update id="update">
        UPDATE book
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="cover != null and cover != ''">
                cover = #{cover},
            </if>
            <if test="publisher != null and publisher != ''">
                publisher = #{publisher},
            </if>
            <if test="author != null and author != ''">
                author = #{author},
            </if>
            <if test="isbn != null and isbn != ''">
                isbn = #{isbn},
            </if>
            <if test="num != null">
                num = #{num},
            </if>
            <if test="detail != null and detail != ''">
                detail = #{detail},
            </if>
            <if test="bookShelfId != null">
                bookshelf_id = #{bookShelfId},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId},
            </if>
            <if test="isPlanBuy != null">
                is_plan_buy = #{isPlanBuy},
            </if>
            <if test="planBuyTime != null">
                plan_buy_time = #{planBuyTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="batchDelete" parameterType="list">
        DELETE FROM book WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="query" resultMap="BaseResultMap">
        SELECT b.*,
        c.name AS category_name,
        bs.floor,
        bs.area,
        bs.frame,
        (SELECT COUNT(bsave.id)
        FROM book_save bsave
        WHERE bsave.book_id = b.id AND bsave.user_id = #{userId}) AS is_save,
        (SELECT boh.is_return
        FROM book_order_history boh
        WHERE boh.book_id = b.id AND boh.user_id = #{userId}
        ORDER BY boh.id DESC
        LIMIT 1) AS is_return,
        (SELECT COUNT(brh.id)
        FROM book_rss_history brh
        WHERE brh.book_id = b.id AND brh.user_id = #{userId}) AS is_rss
        FROM book b
        LEFT JOIN category c ON c.id = b.category_id
        LEFT JOIN bookshelf bs ON bs.id = b.bookshelf_id
        <where>
            <if test="id != null">
                AND b.id = #{id}
            </if>
            <if test="name != null and name != ''">
                AND b.name LIKE concat('%',#{name},'%')
            </if>
            <if test="publisher != null and publisher != ''">
                AND b.publisher LIKE concat('%',#{publisher},'%')
            </if>
            <if test="categoryId != null">
                AND b.category_id = #{categoryId}
            </if>
            <if test="isPlanBuy != null">
                AND b.is_plan_buy = #{isPlanBuy}
            </if>
            <if test="startTime != null and endTime != null">
                AND b.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        <choose>
            <when test="sortField != null and sortDirection != null">
                <choose>
                    <when test="sortField == 'is_plan_buy'">
                        ORDER BY b.is_plan_buy ${sortDirection}
                    </when>
                    <when test="sortField == 'create_time'">
                        ORDER BY b.create_time ${sortDirection}
                    </when>
                    <otherwise>
                        ORDER BY b.create_time DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY b.create_time DESC
            </otherwise>
        </choose>
        <if test="current != null and size != null">
            LIMIT #{current},#{size}
        </if>
    </select>

    <select id="queryCount" resultType="integer">

        SELECT COUNT(*)
        FROM book b
        <where>
            <if test="id != null">
                AND b.id = #{id}
            </if>
            <if test="name != null and name != ''">
                AND b.name LIKE concat('%',#{name},'%')
            </if>
            <if test="publisher != null and publisher != ''">
                AND b.publisher LIKE concat('%',#{publisher},'%')
            </if>
            <if test="categoryId != null">
                AND b.category_id = #{categoryId}
            </if>
            <if test="isPlanBuy != null">
                AND b.is_plan_buy = #{isPlanBuy}
            </if>
            <if test="startTime != null and endTime != null">
                AND b.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>

    </select>

    <select id="findById" resultType="cn.kmbeast.pojo.entity.Book">
        SELECT
            id,
            name,
            cover,
            publisher,
            author,
            isbn,
            num,
            detail,
            bookshelf_id as bookShelfId,
            category_id as categoryId,
            is_plan_buy as isPlanBuy,
            plan_buy_time as planBuyTime,
            create_time as createTime
        FROM book
        WHERE id = #{id}
    </select>

    <!-- 根据类别查询未借阅过的图书 -->
    <select id="getBooksByCategories" resultType="cn.kmbeast.pojo.vo.BookVO">
        SELECT
            b.id, b.name, b.author, b.cover,
            b.publisher, b.create_time as publishTime,
            b.detail as description, c.name as categoryName
        FROM book b
        JOIN category c ON b.category_id = c.id
        WHERE b.category_id IN
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND b.id NOT IN (
            SELECT book_id FROM book_order_history
            WHERE user_id = #{userId} AND is_return = 0
        )
        ORDER BY RAND()
        LIMIT #{limit}
    </select>

    <!-- 获取热门图书 -->
    <select id="getPopularBooks" resultMap="BaseResultMap">
        SELECT
            b.id,
            b.name,
            b.author,
            b.cover,
            b.publisher,
            b.category_id,
            b.isbn,
            b.num,
            b.detail,
            b.create_time,
            c.name as category_name,
            IFNULL(bs.floor, '未知') as floor,
            IFNULL(bs.area, '未知') as area,
            IFNULL(bs.frame, '未知') as frame,
            IFNULL(boh.borrow_count, 0) as borrow_count
        FROM book b
        JOIN category c ON b.category_id = c.id
        LEFT JOIN bookshelf bs ON b.bookshelf_id = bs.id
        LEFT JOIN (
            SELECT book_id, SUM(deadline_num) as borrow_count
            FROM book_order_history
            WHERE is_return = 0 -- 只统计未归还的借阅记录
            GROUP BY book_id

            -- 此处统计的是当前正在被借阅的图书总数量
        ) boh ON b.id = boh.book_id
        WHERE b.num > 0
          AND b.category_id IS NOT NULL  <!-- 确保有分类ID -->
        ORDER BY IFNULL(boh.borrow_count, 0) DESC, b.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 获取最新图书 -->
    <select id="getNewBooks" resultMap="BaseResultMap">
        SELECT
            b.id,
            b.name,
            b.author,
            b.cover,
            b.publisher,
            b.category_id,
            b.isbn,
            b.detail,
            b.create_time,
            c.name as category_name
        FROM book b
        JOIN category c ON b.category_id = c.id
        WHERE b.category_id IS NOT NULL  <!-- 确保有分类ID -->
        ORDER BY b.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 根据用户收藏记录获取图书分类偏好 -->
    <select id="getCategoryPreferenceByUserSave" resultType="cn.kmbeast.pojo.vo.CategoryPreference">
        SELECT
            c.id AS categoryId,
            c.name AS categoryName,
            COUNT(*) AS count,
            CURRENT_TIMESTAMP AS latestTime
        FROM book_save bs
        JOIN book b ON bs.book_id = b.id
        JOIN category c ON b.category_id = c.id
        WHERE bs.user_id = #{userId}
        GROUP BY c.id, c.name
        ORDER BY count DESC
    </select>

    <!-- 根据用户订阅记录获取图书分类偏好 -->
    <select id="getCategoryPreferenceByUserRss" resultType="cn.kmbeast.pojo.vo.CategoryPreference">
        SELECT
            c.id AS categoryId,
            c.name AS categoryName,
            COUNT(*) AS count,
            MAX(brh.create_time) AS latestTime
        FROM book_rss_history brh
        JOIN book b ON brh.book_id = b.id
        JOIN category c ON b.category_id = c.id
        WHERE brh.user_id = #{userId}
        GROUP BY c.id, c.name
        ORDER BY count DESC
    </select>

    <!-- 根据加权分类偏好获取推荐图书 (优化版) -->
    <select id="getRecommendedBooksByCategories" resultMap="BaseResultMap">
        SELECT
            b.id,
            b.name,
            b.author,
            b.cover,
            b.publisher,
            b.category_id,
            b.isbn,
            b.num,
            b.is_plan_buy,
            b.plan_buy_time,
            b.create_time,
            b.detail,
            c.name as category_name,
            IFNULL(bs.floor, '未知') as floor,
            IFNULL(bs.area, '未知') as area,
            IFNULL(bs.frame, '未知') as frame,
            CASE
                <foreach collection="categoryScores.keys" item="categoryId">
                    WHEN b.category_id = #{categoryId} THEN #{categoryScores[${categoryId}]}
                </foreach>
                ELSE 0
            END as category_score
        FROM
            book b
        JOIN
            category c ON b.category_id = c.id
        LEFT JOIN
            bookshelf bs ON b.bookshelf_id = bs.id
        WHERE
            b.num > 0   <!-- 只推荐有库存的图书 -->
            AND b.category_id IS NOT NULL  <!-- 确保有分类ID -->
            AND NOT EXISTS (
                SELECT 1 FROM book_order_history
                WHERE book_id = b.id AND user_id = #{userId} AND is_return = 0
            )
            <!-- 严格匹配用户偏好分类 -->
            AND (
                <foreach collection="categoryScores.keys" item="categoryId" separator=" OR ">
                    b.category_id = #{categoryId}
                </foreach>
            )
        ORDER BY
            category_score DESC,
            RAND() <!-- 增加随机因素，实现不同分类图书交错显示 -->
        LIMIT #{limit}
    </select>

    <!-- 获取图书借阅总数量 -->
    <select id="getBookOrderCount" resultType="java.lang.Integer">
        <!-- deadline_num存储的是借阅数量，此处统计指定图书当前被借阅的总数量 -->
        SELECT IFNULL(SUM(deadline_num), 0) FROM book_order_history WHERE book_id = #{bookId} AND is_return = 0
    </select>

    <!-- 获取图书收藏与借阅关系统计 -->
    <select id="getBookSaveBorrowStats" resultType="cn.kmbeast.pojo.vo.BookSaveBorrowVO">
        SELECT
            b.id,
            b.name,
            b.author,
            b.category_id AS categoryId,
            c.name AS categoryName,
            (SELECT COUNT(*) FROM book_save bs WHERE bs.book_id = b.id) AS saveCount,
            (SELECT IFNULL(SUM(deadline_num), 0) FROM book_order_history boh WHERE boh.book_id = b.id AND boh.is_return = 0) AS borrowCount, -- deadline_num为借阅数量
            b.num AS stockCount
        FROM book b
        JOIN category c ON b.category_id = c.id
        WHERE (SELECT COUNT(*) FROM book_save bs WHERE bs.book_id = b.id) &gt; 0
           OR (SELECT COUNT(*) FROM book_order_history boh WHERE boh.book_id = b.id) &gt; 0
        ORDER BY borrowCount DESC, saveCount DESC
        LIMIT #{limit}
    </select>

    <!-- 获取用户综合分类偏好（整合借阅、收藏、订阅数据） -->
    <select id="getUserCategoryPreferences" resultType="cn.kmbeast.pojo.vo.CategoryPreference">
        SELECT
            c.id as categoryId,
            c.name as categoryName,
            COUNT(*) as count,
            MAX(COALESCE(
                oh.create_time,
                DATE_SUB(CURRENT_TIMESTAMP, INTERVAL (bs.id % 30) DAY),
                brh.create_time
            )) as latestTime
        FROM category c
        LEFT JOIN book b ON b.category_id = c.id
        LEFT JOIN book_order_history oh ON b.id = oh.book_id AND oh.user_id = #{userId}
        LEFT JOIN book_save bs ON b.id = bs.book_id AND bs.user_id = #{userId}
        LEFT JOIN book_rss_history brh ON b.id = brh.book_id AND brh.user_id = #{userId}
        WHERE oh.id IS NOT NULL OR bs.id IS NOT NULL OR brh.id IS NOT NULL
        GROUP BY c.id, c.name
        ORDER BY COUNT(*) DESC, latestTime DESC
    </select>

    <resultMap id="BaseResultMap" type="cn.kmbeast.pojo.vo.BookVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="cover" property="cover"/>
        <result column="publisher" property="publisher"/>
        <result column="author" property="author"/>
        <result column="isbn" property="isbn"/>
        <result column="num" property="num"/>
        <result column="detail" property="detail"/>
        <result column="bookshelf_id" property="bookShelfId"/>
        <result column="category_id" property="categoryId"/>
        <result column="is_plan_buy" property="isPlanBuy"/>
        <result column="plan_buy_time" property="planBuyTime"/>
        <result column="create_time" property="createTime"/>
        <result column="category_name" property="categoryName"/>
        <result column="floor" property="floor"/>
        <result column="area" property="area"/>
        <result column="frame" property="frame"/>
        <result column="is_save" property="isSave"/>
        <result column="is_rss" property="isRss"/>
        <result column="is_return" property="isReturn"/>
        <result column="borrow_count" property="borrowCount"/>
    </resultMap>


</mapper>
