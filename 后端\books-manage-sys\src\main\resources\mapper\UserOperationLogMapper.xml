<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.kmbeast.mapper.UserOperationLogMapper">

    <insert id="save">
        INSERT INTO user_operation_log (user_id, content, create_time)
        VALUES
        <foreach collection="list" item="userOperationLog" separator=",">
            (#{userOperationLog.userId}, #{userOperationLog.content},#{userOperationLog.createTime})
        </foreach>
    </insert>

    <update id="update">
        UPDATE user_operation_log
        <set>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="batchDelete" parameterType="list">
        DELETE FROM user_operation_log WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="query" resultMap="BaseResultMap">

        SELECT uol.*,u.user_name
        FROM user_operation_log uol
        LEFT JOIN user u ON u.id = uol.user_id
        <where>
            <if test="userId != null">
                AND uol.user_id = #{userId}
            </if>
            <if test="content != null and content != ''">
                AND uol.content LIKE concat("%",#{content},'%')
            </if>
            <if test="startTime != null and endTime != null">
                AND uol.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="userName != null and userName != ''">
                AND u.user_name LIKE concat("%",#{userName},'%')
            </if>
        </where>
        ORDER BY uol.create_time DESC
        <if test="current != null and size != null">
            LIMIT #{current},#{size}
        </if>
    </select>

    <select id="queryCount" resultType="integer">

        SELECT COUNT(*)
        FROM user_operation_log uol
        LEFT JOIN user u ON u.id = uol.user_id
        <where>
            <if test="userId != null">
                AND uol.user_id = #{userId}
            </if>
            <if test="startTime != null and endTime != null">
                AND uol.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="userName != null and userName != ''">
                AND u.user_name LIKE concat("%",#{userName},'%')
            </if>
        </where>

    </select>

    <resultMap id="BaseResultMap" type="cn.kmbeast.pojo.vo.UserOperationLogVO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="content" property="content"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 获取用户活跃度统计（按操作类型和分组方式） -->
    <select id="getUserActivityStats" resultType="cn.kmbeast.pojo.vo.UserActivityVO">
        SELECT
            CASE
                WHEN user_operation_log.content LIKE '%借阅%' THEN '借阅'
                WHEN user_operation_log.content LIKE '%收藏%' THEN '收藏'
                WHEN user_operation_log.content LIKE '%浏览%' THEN '浏览'
                WHEN user_operation_log.content LIKE '%订阅%' THEN '订阅'
                WHEN user_operation_log.content LIKE '%登录%' THEN '登录'
                WHEN user_operation_log.content LIKE '%查询%' OR user_operation_log.content LIKE '%搜索%' THEN '查询'
                ELSE '其他'
            END AS activityType,
            COUNT(*) AS count,
            CASE
                <if test="groupBy == 'role'">
                    WHEN u.user_role = 'admin' THEN 'admin'
                    ELSE 'user'
                </if>
                <if test="groupBy == 'dayPart' or groupBy == null">
                    WHEN HOUR(user_operation_log.create_time) BETWEEN 5 AND 9 THEN 'morning'
                    WHEN HOUR(user_operation_log.create_time) BETWEEN 10 AND 13 THEN 'noon'
                    WHEN HOUR(user_operation_log.create_time) BETWEEN 14 AND 17 THEN 'afternoon'
                    ELSE 'evening'
                </if>
            END AS timeType
        FROM user_operation_log
        <if test="groupBy == 'role'">
            LEFT JOIN user u ON user_operation_log.user_id = u.id
        </if>
        WHERE user_operation_log.create_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY activityType, timeType
    </select>

</mapper>
