# 📚 图书管理系统技术文档

## 目录

- [一、项目概述](#一项目概述)
  - [1.1 系统定位](#11-系统定位-)
  - [1.2 核心功能亮点](#12-核心功能亮点-)
  - [1.3 用户角色划分](#13-用户角色划分-)
- [二、技术架构](#二技术架构-)
  - [2.1 整体架构图](#21-整体架构图)
  - [2.2 技术栈选型表](#22-技术栈选型表)
- [三、项目结构](#三项目结构-)
  - [3.1 后端结构](#31-后端结构)
  - [3.2 前端结构](#32-前端结构)
  - [3.3 前端组件设计](#33-前端组件设计)
- [四、核心模块设计](#四核心模块设计-)
  - [4.1 核心业务流程图](#41-核心业务流程图)
  - [4.2 典型功能实现](#42-典型功能实现)
    - [4.2.1 图书借阅流程](#421-图书借阅流程-)
    - [4.2.2 图书订阅通知机制](#422-图书订阅通知机制-)
    - [4.2.3 个性化图书推荐系统](#423-个性化图书推荐系统-)
    - [4.2.4 社区化留言系统](#424-社区化留言系统-)
    - [4.2.5 用户行为日志记录](#425-用户行为日志记录-)
    - [4.2.6 文件上传管理](#426-文件上传管理-)
    - [4.2.7 数据可视化统计](#427-数据可视化统计-)
- [五、关键技术方案](#五关键技术方案-)
  - [5.1 异常处理机制](#51-异常处理机制)
  - [5.2 权限控制方案](#52-权限控制方案)
  - [5.3 数据验证策略](#53-数据验证策略)
  - [5.4 日志监控体系](#54-日志监控体系)
  - [5.5 分页实现方案](#55-分页实现方案)
  - [5.6 安全防护机制](#56-安全防护机制)
  - [5.7 跨域处理方案](#57-跨域处理方案)
  - [5.8 数据库设计](#58-数据库设计-)
    - [5.8.1 核心表设计](#581-核心表设计)
    - [5.8.2 借阅与推荐相关表](#582-借阅与推荐相关表)
    - [5.8.3 订阅与通知表](#583-订阅与通知表)
    - [5.8.4 社区留言相关表](#584-社区留言相关表)
    - [5.8.5 用户与系统表](#585-用户与系统表)
- [六、部署说明](#六部署说明-)
  - [6.1 环境依赖清单](#61-环境依赖清单)
  - [6.2 启动配置指引](#62-启动配置指引)
  - [6.3 常见问题排错](#63-常见问题排错)
  - [6.4 部署架构建议](#64-部署架构建议)
- [七、附录](#七附录-)
  - [7.1 接口文档地址](#71-接口文档地址)
  - [7.2 代码规范说明](#72-代码规范说明)
  - [7.3 参考资料](#73-参考资料)
  - [7.4 项目特色总结](#74-项目特色总结)


## 一、项目概述

### 1.1 系统定位 🎯

图书管理系统是一个基于SpringBoot和Vue的全栈应用，旨在提供完整的图书管理解决方案，包括图书借阅、归还、预约、订阅等核心功能，满足图书馆日常运营需求。系统采用前后端分离架构，提供直观的用户界面和高效的后台管理功能，适用于学校、社区和企业图书馆等场景。

### 1.2 核心功能亮点 ✨

- **完整的借阅流程**：支持用户查找、借阅、归还图书的全流程管理，包含借阅期限设置和到期提醒
- **订阅通知系统**：用户可订阅预售的图书，系统自动发送上架通知，提升用户体验和图书馆采购参考
- **用户行为日志**：基于AOP技术的用户行为全程记录，方便追踪与分析，支持多维度查询和导出
- **多维度图书管理**：包括书籍分类、书架管理等多角度的图书资源管理，支持批量操作和快速检索
- **可视化统计报表**：管理员首页提供图书借阅、用户活跃度等数据可视化展示，支持多种图表类型和时间范围筛选
- **个性化图书推荐**：基于多维度用户行为分析的智能推荐系统，结合时间衰减因子和标签均衡机制，提供精准的个性化推荐、热门借阅和新书速递功能
- **社区化留言系统**：楼中楼评论结构，支持点赞、收藏、标签分类、通知消息，管理员可设置置顶与精华，提升用户互动与社区活跃度

### 1.3 用户角色划分 👥

- **管理员**：负责系统管理、图书管理、用户管理等后台操作，拥有全部功能权限
- **普通用户**：进行图书借阅、归还、收藏、订阅等日常操作，参与社区讨论和反馈

## 二、技术架构 🔧

### 2.1 整体架构图

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  前端展示层     │─────▶│  后端服务层     │─────▶│  数据持久层     │
│  Vue + ElementUI│      │  SpringBoot     │      │  MySQL          │
│                 │◀─────│  + SpringMVC    │◀─────│                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
```

系统采用经典三层架构，实现前后端分离：
- **前端展示层**：负责用户界面展示和交互，采用Vue.js框架和Element UI组件库
- **后端服务层**：处理业务逻辑和数据处理，基于SpringBoot和SpringMVC实现RESTful API
- **数据持久层**：负责数据存储和访问，使用MySQL数据库和MyBatis ORM框架

### 2.2 技术栈选型表

| 层级       | 技术组件                            | 版本                | 说明                                |
|------------|-----------------------------------|---------------------|-----------------------------------|
| 前端框架   | Vue.js                            | 2.6.11              | 渐进式JavaScript框架               |
|            | Axios                             | 0.21.1              | 基于Promise的HTTP客户端            |
|            | Vue Router                        | 3.2.0               | Vue.js官方路由管理器               |
|            | Element UI                        | 2.15.14             | 基于Vue的桌面端组件库              |
|            | ECharts                           | 5.6.0               | 数据可视化图表库                   |
| 后端框架   | SpringBoot                        | 2.x                 | 简化Spring应用开发框架             |
|            | SpringMVC                         | 与SpringBoot集成    | Web应用和REST服务开发框架          |
|            | MyBatis                           | 与SpringBoot集成    | 优秀的持久层框架                   |
| 数据存储   | MySQL                             | 8.0.34              | 关系型数据库                       |
| 基础设施   | JDK                               | 1.8                 | Java开发工具包                     |
|            | Maven                             | 3.6.1+              | 项目管理工具                       |
|            | Node.js                           | 14.16.0+            | JavaScript运行环境                 |

## 三、项目结构 📂

### 3.1 后端结构

```
后端/books-manage-sys/src/main/java/cn/kmbeast/
├── controller/   # API接口层，处理HTTP请求和响应
│   ├── BookController.java                    # 图书基本信息管理接口
│   ├── BookOrderHistoryController.java        # 图书借阅历史管理接口
│   ├── BookRssHistoryController.java          # 图书订阅历史管理接口
│   ├── BookSaveController.java                # 图书收藏管理接口
│   ├── BookshelfController.java               # 书架管理接口
│   ├── CategoryController.java                # 图书分类管理接口
│   ├── ChartController.java                   # 图表数据接口
│   ├── FileController.java                    # 文件上传下载接口
│   ├── NoticeController.java                  # 系统公告管理接口
│   ├── ReaderProposalController.java          # 读者留言管理接口
│   ├── ReaderProposalCategoryController.java  # 留言分类标签管理接口
│   ├── RssNotificationController.java         # 订阅通知管理接口
│   ├── SimpleRecommendationController.java    # 图书推荐系统接口
│   ├── UserController.java                    # 用户管理接口
│   ├── UserOperationLogController.java        # 用户操作日志接口
│   └── ViewsController.java                   # 数据可视化统计接口
├── service/      # 业务逻辑层，实现核心业务功能
│   ├── impl/     # 接口实现类
│   │   ├── BookOrderHistoryServiceImpl.java   # 图书借阅业务实现
│   │   ├── BookRssHistoryServiceImpl.java     # 图书订阅业务实现
│   │   ├── BookSaveServiceImpl.java           # 图书收藏业务实现
│   │   ├── BookServiceImpl.java               # 图书基本信息业务实现
│   │   ├── BookshelfServiceImpl.java          # 书架管理业务实现
│   │   ├── CategoryServiceImpl.java           # 图书分类业务实现
│   │   ├── ChartServiceImpl.java              # 图表数据业务实现
│   │   ├── NoticeServiceImpl.java             # 系统公告业务实现
│   │   ├── ReaderProposalServiceImpl.java     # 读者留言业务实现
│   │   ├── ReaderProposalCategoryServiceImpl.java # 留言分类标签业务实现
│   │   ├── RssNotificationServiceImpl.java    # 订阅通知业务实现
│   │   ├── SimpleRecommendationServiceImpl.java # 图书推荐算法实现
│   │   ├── UserOperationLogServiceImpl.java   # 用户操作日志业务实现
│   │   ├── UserServiceImpl.java               # 用户管理业务实现
│   │   └── ViewsServiceImpl.java              # 数据统计业务实现
│   ├── BookOrderHistoryService.java           # 图书借阅业务接口
│   ├── BookRssHistoryService.java             # 图书订阅业务接口
│   ├── BookSaveService.java                   # 图书收藏业务接口
│   ├── BookService.java                       # 图书基本信息业务接口
│   ├── BookshelfService.java                  # 书架管理业务接口
│   ├── CategoryService.java                   # 图书分类业务接口
│   ├── ChartService.java                      # 图表数据业务接口
│   ├── NoticeService.java                     # 系统公告业务接口
│   ├── ReaderProposalService.java             # 读者留言业务接口
│   ├── ReaderProposalCategoryService.java     # 留言分类标签业务接口
│   ├── RssNotificationService.java            # 订阅通知业务接口
│   ├── SimpleRecommendationService.java       # 图书推荐业务接口
│   ├── UserOperationLogService.java           # 用户操作日志业务接口
│   ├── UserService.java                       # 用户管理业务接口
│   └── ViewsService.java                      # 数据统计业务接口
├── mapper/       # 数据持久层，与数据库交互
│   ├── BookMapper.java                        # 图书数据访问接口
│   ├── BookOrderHistoryMapper.java            # 借阅历史数据访问接口
│   ├── BookRssHistoryMapper.java              # 订阅历史数据访问接口
│   ├── BookSaveMapper.java                    # 图书收藏数据访问接口
│   ├── BookShelfMapper.java                   # 书架数据访问接口
│   ├── CategoryMapper.java                    # 分类数据访问接口
│   ├── NoticeMapper.java                      # 公告数据访问接口
│   ├── ReaderProposalMapper.java              # 读者留言数据访问接口
│   ├── ReaderProposalCategoryMapper.java      # 留言分类数据访问接口
│   ├── ReaderProposalLikeMapper.java          # 留言点赞数据访问接口
│   ├── ReaderProposalSaveMapper.java          # 留言收藏数据访问接口
│   ├── RssNotificationMapper.java             # 订阅通知数据访问接口
│   ├── UserMapper.java                        # 用户数据访问接口
│   └── UserOperationLogMapper.java            # 操作日志数据访问接口
├── resources/    # 资源文件目录
│   ├── mapper/   # MyBatis映射文件，定义SQL语句
│   │   ├── BookMapper.xml                     # 图书相关SQL，包含推荐查询
│   │   ├── BookOrderHistoryMapper.xml         # 借阅历史SQL，包含用户行为分析
│   │   ├── BookRssHistoryMapper.xml           # 订阅历史SQL映射
│   │   ├── BookSaveMapper.xml                 # 收藏相关SQL，包含统计查询
│   │   ├── BookshelfMapper.xml                # 书架相关SQL映射
│   │   ├── CategoryMapper.xml                 # 分类相关SQL映射
│   │   ├── NoticeMapper.xml                   # 公告相关SQL映射
│   │   ├── ReaderProposalMapper.xml           # 留言系统相关SQL，包含楼中楼查询
│   │   ├── ReaderProposalCategoryMapper.xml   # 留言分类SQL映射
│   │   ├── ReaderProposalLikeMapper.xml       # 留言点赞SQL映射
│   │   ├── ReaderProposalSaveMapper.xml       # 留言收藏SQL映射
│   │   ├── RssNotificationMapper.xml          # 订阅通知SQL映射
│   │   ├── UserMapper.xml                     # 用户相关SQL映射
│   │   └── UserOperationLogMapper.xml         # 操作日志SQL映射
├── pojo/         # 数据对象，定义系统中的数据模型
│   ├── entity/   # 数据实体，与数据库表对应
│   │   ├── Book.java                          # 图书实体
│   │   ├── BookOrderHistory.java              # 借阅历史实体
│   │   ├── BookRssHistory.java                # 订阅历史实体
│   │   ├── BookSave.java                      # 图书收藏实体
│   │   ├── BookShelf.java                     # 书架实体
│   │   ├── Category.java                      # 分类实体
│   │   ├── Notice.java                        # 公告实体
│   │   ├── ReaderProposal.java                # 读者留言实体
│   │   ├── ReaderProposalCategory.java        # 留言分类实体
│   │   ├── RssNotification.java               # 订阅通知实体
│   │   ├── User.java                          # 用户实体
│   │   └── UserOperationLog.java              # 用户操作日志实体
│   ├── vo/       # 视图对象，用于前端展示
│   │   ├── BookBehaviorVO.java                # 图书行为视图对象
│   │   ├── BookOrderHistoryVO.java            # 借阅历史视图对象
│   │   ├── BookRssHistoryVO.java              # 订阅历史视图对象
│   │   ├── BookSaveBorrowVO.java              # 图书收藏借阅视图对象
│   │   ├── BookSaveVO.java                    # 图书收藏视图对象
│   │   ├── BookVO.java                        # 图书视图对象
│   │   ├── BorrowProcessVO.java               # 借阅流程视图对象
│   │   ├── CategoryPreference.java            # 分类偏好对象
│   │   ├── CategoryStatVO.java                # 分类统计视图对象
│   │   ├── ChartVO.java                       # 图表数据视图对象
│   │   ├── ReaderProposalVO.java              # 读者留言视图对象
│   │   ├── RssNotificationVO.java             # 订阅通知视图对象
│   │   ├── SystemVisitVO.java                 # 系统访问视图对象
│   │   ├── UserActivityVO.java                # 用户活跃度视图对象
│   │   ├── UserOperationLogVO.java            # 操作日志视图对象
│   │   └── UserVO.java                        # 用户视图对象
│   ├── dto/      # 数据传输对象，用于接口参数传递
│   │   ├── query/ # 查询条件类
│   │   │   ├── base/                          # 基础查询条件
│   │   │   └── extend/                        # 扩展查询条件
│   │   │       ├── BookOrderHistoryQueryDto.java  # 借阅历史查询条件
│   │   │       ├── BookQueryDto.java              # 图书查询条件
│   │   │       ├── BookRssHistoryQueryDto.java    # 订阅历史查询条件
│   │   │       ├── BookSaveQueryDto.java          # 收藏查询条件
│   │   │       ├── BookshelfQueryDto.java         # 书架查询条件
│   │   │       ├── CategoryQueryDto.java          # 分类查询条件
│   │   │       ├── NoticeQueryDto.java            # 公告查询条件
│   │   │       ├── ReaderProposalQueryDto.java    # 留言查询条件
│   │   │       ├── RssNotificationQueryDto.java   # 通知查询条件
│   │   │       ├── UserOperationLogQueryDto.java  # 日志查询条件
│   │   │       └── UserQueryDto.java              # 用户查询条件
│   │   └── update/                            # 更新对象目录
│   ├── api/      # API响应对象，统一响应格式
│   └── em/       # 枚举类，定义系统枚举值
├── aop/          # AOP切面，实现横切关注点
│   ├── Log.java                               # 日志注解
│   ├── LogAspect.java                         # 日志切面实现
│   ├── Pager.java                             # 分页注解
│   ├── PagerAspect.java                       # 分页切面实现
│   ├── Protector.java                         # 权限保护注解
│   └── ProtectorAspect.java                   # 权限保护切面实现
├── config/       # 配置类，系统配置
│   ├── InterceptorConfig.java                 # 拦截器配置
│   └── WebConfig.java                         # Web配置
├── context/      # 上下文管理
│   └── LocalThreadHolder.java                 # 线程本地变量，存储用户信息
├── Interceptor/  # 拦截器，处理请求拦截
└── utils/        # 工具类，通用功能实现
    ├── DateUtil.java                          # 日期工具类
    ├── IdFactoryUtil.java                     # ID生成工具类
    ├── JwtUtil.java                           # JWT工具类
    └── PathUtils.java                         # 路径工具类
```

### 3.2 前端结构

```
src/
├── views/                # 页面组件，按功能模块划分
│   ├── admin/            # 管理员页面
│   │   ├── BookManage.vue                # 图书管理页面
│   │   ├── BookOrderHistoryManage.vue    # 借阅记录管理页面
│   │   ├── BookRssHistoryManage.vue      # 订阅记录管理页面
│   │   ├── BookshelfManage.vue           # 书架管理页面
│   │   ├── CategoryManage.vue            # 分类管理页面
│   │   ├── CreateNotice.vue              # 创建公告页面
│   │   ├── Home.vue                      # 管理员主页框架
│   │   ├── Main.vue                      # 管理员首页(数据统计)
│   │   ├── NoticeManage.vue              # 公告管理页面
│   │   ├── ReaderProposalManage.vue      # 留言管理页面
│   │   ├── ReaderProposalCategoryManage.vue # 留言分类管理页面
│   │   ├── RssNotificationManage.vue     # 订阅通知管理页面
│   │   ├── UserManage.vue                # 用户管理页面
│   │   └── UserOperationLogManage.vue    # 操作日志管理页面
│   ├── user/             # 用户页面
│   │   ├── BookOperation.vue             # 图书借阅操作页面
│   │   ├── BookOrderHistory.vue          # 个人借阅历史页面
│   │   ├── BookRssHistory.vue            # 个人订阅历史页面
│   │   ├── BookSave.vue                  # 个人收藏页面
│   │   ├── Home.vue                      # 用户主页框架
│   │   ├── Main.vue                      # 社区留言主页
│   │   ├── MySelf.vue                    # 个人中心页面
│   │   ├── Notice.vue                    # 公告列表页面
│   │   ├── NoticeDetail.vue              # 公告详情页面
│   │   ├── ReaderProposal.vue            # 留言详情页面
│   │   ├── ResetPwd.vue                  # 重置密码页面
│   │   ├── RssNotification.vue           # 订阅通知页面
│   │   ├── Self.vue                      # 个人信息页面
│   │   └── UserDashboard.vue             # 用户首页(包含个性化推荐)
│   ├── login/            # 登录相关页面
│   │   └── Login.vue                     # 登录页面
│   └── register/         # 注册相关页面
│       └── Register.vue                  # 注册页面
├── router/               # 路由配置
│   └── index.js                          # 路由定义和权限控制
├── components/           # 公共组件，可复用UI元素
│   ├── BookCard.vue                      # 图书卡片组件(含3D翻转效果)
│   ├── VerticalMenu.vue                  # 垂直菜单组件
│   ├── PieChart.vue                      # 饼图组件
│   ├── LevelHeader.vue                   # 层级标题组件
│   ├── LineChart.vue                     # 折线图组件
│   ├── LevelMenu.vue                     # 层级菜单组件
│   ├── Logo.vue                          # 系统Logo组件
│   ├── Editor.vue                        # 富文本编辑器组件
│   ├── Evaluations.vue                   # 评价组件
│   ├── Inteval.vue                       # 时间间隔组件
│   ├── TagLine.vue                       # 标签行组件
│   ├── BarChart.vue                      # 柱状图组件
│   ├── FunnelChart.vue                   # 漏斗图组件
│   ├── GaugeChart.vue                    # 仪表盘图表组件
│   ├── HorizontalBarChart.vue            # 水平柱状图组件
│   ├── MiniBarChart.vue                  # 迷你柱状图组件
│   ├── MiniCircularProgress.vue          # 迷你圆形进度条组件
│   ├── MiniDonutChart.vue                # 迷你环形图组件
│   ├── MiniLineChart.vue                 # 迷你折线图组件
│   ├── MiniProgressBar.vue               # 迷你进度条组件
│   ├── NestedReplies.vue                 # 嵌套回复组件
│   ├── RadarChart.vue                    # 雷达图组件
│   └── ScatterChart.vue                  # 散点图组件
├── utils/                # 工具类，通用功能实现
│   ├── colorUtils.js                     # 颜色工具类
│   ├── data.js                           # 数据工具类
│   ├── dateUtil.js                       # 日期工具类
│   ├── echarts.js                        # ECharts配置工具
│   ├── eventBus.js                       # 事件总线
│   ├── request.js                        # HTTP请求封装
│   ├── storage.js                        # 本地存储工具
│   └── swalPlugin.js                     # SweetAlert插件配置
├── assets/               # 静态资源文件
│   ├── css/              # 样式文件
│   │   ├── basic.scss                    # 基础样式
│   │   ├── button.scss                   # 按钮样式
│   │   ├── common-components.scss        # 公共组件样式
│   │   ├── dialog.scss                   # 对话框样式
│   │   ├── editor.scss                   # 编辑器样式
│   │   ├── elementui-cover.scss          # ElementUI覆盖样式
│   │   ├── iconfont.css                  # 图标字体样式
│   │   ├── input.scss                    # 输入框样式
│   │   ├── sweetalert.scss               # 弹窗提示样式
│   │   ├── tag-styles.scss               # 标签样式
│   │   └── theme-variables.scss          # 主题变量样式
│   ├── fonts/            # 字体文件
│   │   ├── iconfont.js                   # 图标字体脚本
│   │   ├── iconfont.ttf                  # 图标字体文件
│   │   ├── iconfont.woff                 # 图标字体文件
│   │   └── iconfont.woff2                # 图标字体文件
│   └── img/              # 图片资源
│       ├── logo.png                      # 系统Logo
│       ├── yuedu.png                     # 阅读主题图片
│       └── yuedu1.png                    # 阅读主题图片
├── App.vue               # 根组件
└── main.js               # 项目入口文件
```

### 3.3 前端组件设计

- **公共组件**：位于`components`目录，提供通用UI元素，实现高度复用
- **布局模式**：采用Element UI的栅格系统和Flex布局，实现响应式设计
- **状态管理**：使用Vue Router进行路由管理，eventBus实现组件间通信，保持状态同步
- **API封装**：通过Axios实例统一处理请求和响应，包含拦截器和统一错误处理
- **组件复用**：页面组件模块化设计，实现代码复用和维护简化
- **样式管理**：采用SCSS预处理器，模块化样式文件，实现主题定制
- **权限控制**：基于路由守卫实现页面访问权限控制，区分管理员和普通用户
- **特色组件**：
  - **图书卡片组件**：实现3D翻转效果，包含精美上浮动画和仿真书籍效果
  - **图表组件**：封装ECharts，提供多种数据可视化图表
  - **富文本编辑器**：集成wangEditor，支持图文混排和格式控制
  - **标签系统**：根据不同类型自动分配颜色，提升视觉区分度

## 四、核心模块设计 🔍

### 4.1 核心业务流程图

```
[用户] → [查找图书] → [借阅图书] → [借阅记录] → [归还图书]
                   ↘ [收藏图书] → [收藏记录]
                   ↘ [订阅未有图书] → [订阅记录] → [接收通知]
                   ↘ [参与社区讨论] → [发布/回复留言] → [收到互动通知]
                   ↘ [查看推荐] → [个性化图书推荐] → [发现新书]

[管理员] → [图书管理] → [新增/编辑/删除图书]
         → [书架管理] → [分配图书位置]
         → [分类管理] → [维护图书分类]
         → [借阅管理] → [查看借阅情况]
         → [订阅管理] → [处理订阅请求/发送通知]
         → [社区管理] → [设置置顶/精华/分类标签]
         → [读者反馈] → [处理用户建议]
         → [数据统计] → [查看系统使用情况]
```

系统核心业务流程分为用户端和管理员端：

**用户端流程**：
- 图书查找与借阅：用户可以通过分类、关键词等方式查找图书，进行借阅操作
- 图书收藏：用户可以收藏感兴趣的图书，方便后续查看
- 图书订阅：对于未入库或已借出的图书，用户可以进行订阅，系统会在图书可用时发送通知
- 社区互动：用户可以在社区中发布留言、回复他人，参与讨论
- 个性化推荐：系统根据用户的借阅历史、收藏和订阅记录，提供个性化的图书推荐

**管理员端流程**：
- 图书资源管理：包括图书信息管理、书架管理和分类管理
- 借阅业务管理：监控借阅情况，处理逾期未还等异常情况
- 订阅通知管理：处理用户订阅请求，发送图书到货通知
- 社区内容管理：管理用户留言，设置置顶和精华内容
- 系统数据分析：通过可视化图表查看系统使用情况，辅助决策

### 4.2 典型功能实现

#### 4.2.1 用户登录功能 🔐

- **控制器**：`UserController.java`
- **服务层**：`UserService.java`、`UserServiceImpl.java`
- **数据访问**：`UserMapper.java`
- **前端界面**：`Login.vue`
- **工具类**：`JwtUtil.java`、`MD5加密`

**功能描述**：
用户登录功能采用JWT令牌认证机制，结合MD5双重加密确保密码安全。前端进行角色选择验证，后端验证用户身份并生成JWT令牌，支持管理员和普通用户的角色区分登录。登录成功后根据用户角色自动跳转到对应的管理界面。

**核心实现**：
- 前端MD5双重加密：`md5(md5(password))`确保密码传输安全
- 角色匹配验证：验证用户选择角色与实际角色是否一致
- JWT令牌生成：使用HS256算法生成包含用户ID和角色的令牌
- 自动角色跳转：管理员跳转`/admin`，普通用户跳转`/userDashboard`
- 登录状态检查：验证用户是否被禁用或删除

**关键代码**：
```java
// 后端登录验证
public Result<Object> login(UserLoginDTO userLoginDTO) {
    User user = userMapper.getByActive(
        User.builder().userAccount(userLoginDTO.getUserAccount()).build()
    );
    if (!Objects.equals(userLoginDTO.getUserPwd(), user.getUserPwd())) {
        return ApiResult.error("密码错误");
    }
    String token = JwtUtil.toToken(user.getId(), user.getUserRole());
    Map<String, Object> map = new HashMap<>();
    map.put("token", token);
    map.put("role", user.getUserRole());
    return ApiResult.success("登录成功", map);
}
```

```javascript
// 前端登录处理
async login() {
  const hashedPwd = md5(md5(this.pwd));
  const paramDTO = { userAccount: this.act, userPwd: hashedPwd };
  const { data } = await request.post(`user/login`, paramDTO);

  // 验证角色匹配
  const { role } = data.data;
  if (role !== this.userRole) {
    // 角色不匹配处理
    return;
  }

  setToken(data.data.token);
  this.routeByRole(role);
}
```

#### 4.2.2 用户注册功能 📝

- **控制器**：`UserController.java`
- **服务层**：`UserService.java`、`UserServiceImpl.java`
- **数据访问**：`UserMapper.java`
- **前端界面**：`Register.vue`
- **文件上传**：`FileController.java`

**功能描述**：
用户注册功能提供完整的用户信息录入，包括账号唯一性验证、密码强度检查、头像上传等。系统自动为新用户分配普通用户角色，设置默认状态，并进行MD5双重加密存储密码。注册成功后自动跳转到登录页面。

**核心实现**：
- 账号唯一性验证：实时检查账号是否已被使用
- 密码强度检查：前端实时显示密码强度指示器
- 头像上传功能：支持图片预览和删除，自动生成唯一文件名
- 双重密码确认：确保用户输入密码的准确性
- 默认角色分配：新用户自动设置为普通用户角色

**关键代码**：
```java
// 后端注册处理
public Result<String> register(UserRegisterDTO userRegisterDTO) {
    // 检查账号是否已存在
    User entity = userMapper.getByActive(
        User.builder().userAccount(userRegisterDTO.getUserAccount()).build()
    );
    if (Objects.nonNull(entity)) {
        return ApiResult.error("账号不可用");
    }

    // 创建新用户
    User saveEntity = User.builder()
        .userRole(RoleEnum.USER.getRole())  // 默认普通用户
        .userName(userRegisterDTO.getUserName())
        .userAccount(userRegisterDTO.getUserAccount())
        .userPwd(userRegisterDTO.getUserPwd())  // 已加密密码
        .userAvatar(userRegisterDTO.getUserAvatar())
        .createTime(LocalDateTime.now())
        .isLogin(LoginStatusEnum.USE.getFlag())
        .isWord(WordStatusEnum.USE.getFlag())
        .isDeleted(false).build();

    userMapper.insert(saveEntity);
    return ApiResult.success("注册成功");
}
```

```javascript
// 前端注册处理
async registerFunc() {
  // 密码一致性验证
  if (this.pwd !== this.pwdConfirm) {
    this.$swal.fire({
      title: "填写校验",
      text: "前后密码输入不一致",
      icon: "error"
    });
    return;
  }

  // 双重MD5加密
  const hashedPwd = md5(md5(this.pwd));
  const paramDTO = {
    userAccount: this.act,
    userPwd: hashedPwd,
    userName: this.name,
    userAvatar: this.userAvatar
  };

  const { data } = await request.post(`user/register`, paramDTO);
  // 注册成功跳转登录页
}
```

#### 4.2.3 分页功能 📄

- **AOP注解**：`Pager.java`
- **AOP切面**：`PagerAspect.java`
- **基础DTO**：`QueryDto.java`
- **结果封装**：`PageResult.java`
- **前端组件**：各管理页面的`el-pagination`组件

**功能描述**：
分页功能采用AOP切面编程实现统一的分页处理，通过`@Pager`注解标记需要分页的方法，自动完成前端页码到数据库偏移量的转换。支持动态分页大小设置，统一的分页结果封装，提供良好的用户体验。

**核心实现**：
- AOP自动拦截：`@Pager`注解标记的方法自动进行分页处理
- 参数自动转换：页码(current)转换为偏移量(offset = (current-1) * size)
- 统一结果封装：`PageResult`类封装分页数据和总条数
- 前端分页组件：Element UI的分页组件，支持页码跳转和分页大小调整
- 双查询机制：分别查询分页数据和总条数

**关键代码**：
```java
// AOP分页切面
@Around("@annotation(pager)")
public Object handlePageableParams(ProceedingJoinPoint joinPoint, Pager pager) throws Throwable {
    Object[] args = joinPoint.getArgs();
    for (Object arg : args) {
        if (arg instanceof QueryDto) {
            QueryDto queryDTO = (QueryDto) arg;
            configPager(queryDTO);  // 转换分页参数
        }
    }
    return joinPoint.proceed(args);
}

private void configPager(QueryDto queryDTO) {
    if (queryDTO.getCurrent() != null && queryDTO.getSize() != null) {
        // 页码转偏移量
        int offset = Math.max(0, (queryDTO.getCurrent() - 1) * queryDTO.getSize());
        queryDTO.setCurrent(offset);
    }
}
```

```javascript
// 前端分页处理
async fetchFreshData() {
  const params = {
    current: this.currentPage,  // 当前页
    size: this.pageSize,        // 页面大小
    ...this.queryDto,           // 其他查询条件
  };

  const response = await this.$axios.post("/query", params);
  const { data } = response;
  this.tableData = data.data;    // 分页数据
  this.totalItems = data.total;  // 总条数
}

// 分页事件处理
handleSizeChange(val) {
  this.pageSize = val;
  this.currentPage = 1;
  this.fetchFreshData();
}

handleCurrentChange(val) {
  this.currentPage = val;
  this.fetchFreshData();
}
```

#### 4.2.4 模糊搜索功能 🔍

- **数据访问**：各Mapper.xml文件中的动态SQL
- **核心技术**：MySQL LIKE操作符 + MyBatis动态SQL
- **前端界面**：各管理页面的搜索框组件
- **应用场景**：用户搜索、图书搜索、日志搜索等

**功能描述**：
模糊搜索功能基于MySQL的LIKE操作符实现，通过MyBatis动态SQL支持多字段、多条件的模糊匹配。采用`concat('%',#{参数},'%')`实现前后模糊匹配，支持用户名、图书名称、出版社等多种字段的模糊查询，提供智能搜索体验。

**核心实现**：
- MySQL LIKE模糊匹配：使用`concat('%',#{param},'%')`实现前后模糊查询
- 多字段联合搜索：支持同时搜索多个字段，如用户名、账号、邮箱
- 动态SQL条件：通过MyBatis的`<if>`标签实现条件动态拼接
- 防SQL注入：使用参数化查询`#{}`防止SQL注入攻击
- 智能搜索提示：前端显示搜索结果数量和匹配提示

**关键代码**：
```xml
<!-- 用户模糊搜索 -->
<select id="query" resultMap="BaseResultMap">
    SELECT uol.*, u.user_name
    FROM user_operation_log uol
    LEFT JOIN user u ON u.id = uol.user_id
    <where>
        <if test="userName != null and userName != ''">
            AND u.user_name LIKE concat('%',#{userName},'%')
        </if>
        <if test="userAccount != null and userAccount != ''">
            AND u.user_account LIKE concat('%',#{userAccount},'%')
        </if>
        <if test="userEmail != null and userEmail != ''">
            AND u.user_email LIKE concat('%',#{userEmail},'%')
        </if>
    </where>
    ORDER BY uol.create_time DESC
</select>

<!-- 图书模糊搜索 -->
<select id="query" resultMap="BaseResultMap">
    SELECT b.*, c.name as categoryName
    FROM book b
    LEFT JOIN book_category c ON b.category_id = c.id
    <where>
        <if test="name != null and name != ''">
            AND b.name LIKE concat('%',#{name},'%')
        </if>
        <if test="publisher != null and publisher != ''">
            AND b.publisher LIKE concat('%',#{publisher},'%')
        </if>
        <if test="author != null and author != ''">
            AND b.author LIKE concat('%',#{author},'%')
        </if>
    </where>
</select>
```

```javascript
// 前端搜索处理
async performSearch() {
  // 构建搜索参数
  const searchParams = {
    current: 1,  // 搜索时重置到第一页
    size: this.pageSize,
    userName: this.searchKeyword,
    userAccount: this.searchKeyword,
    // 其他搜索条件...
  };

  const response = await this.$axios.post("/query", searchParams);
  const { data } = response;

  // 显示搜索结果提示
  if (data.total > 0) {
    this.$message.success(`找到 ${data.total} 条匹配记录`);
  } else {
    this.$message.info("未找到匹配的记录");
  }

  this.tableData = data.data;
  this.totalItems = data.total;
}
```

#### 4.2.5 用户管理功能 👥

- **控制器**：`UserController.java`
- **服务层**：`UserService.java`、`UserServiceImpl.java`
- **数据访问**：`UserMapper.java`、`UserMapper.xml`
- **前端界面**：`UserManage.vue`
- **权限控制**：`@Protector(role = "admin")`

**功能描述**：
用户管理功能为管理员提供完整的用户信息管理能力，包括用户查询、新增、编辑、删除、状态管理等。支持多条件搜索、批量操作、账号唯一性验证、密码重置等功能。通过权限控制确保只有管理员可以访问用户管理功能。

**核心实现**：
- 权限控制：使用`@Protector(role = "admin")`确保只有管理员可访问
- 多条件查询：支持用户名、账号、邮箱、角色、状态等多维度搜索
- 批量操作：支持批量删除、状态修改等操作
- 账号验证：实时验证账号唯一性，防止重复注册
- 状态管理：支持登录状态、禁言状态的管理
- 密码安全：新增和修改用户时进行MD5双重加密

**关键代码**：
```java
// 用户查询接口
@Pager
@Protector(role = "admin")
@PostMapping(value = "/query")
public Result<List<User>> query(@RequestBody UserQueryDto userQueryDto) {
    return userService.query(userQueryDto);
}

// 批量删除用户
@Protector(role = "admin")
@PostMapping(value = "/batchDelete")
public Result<String> batchDelete(@RequestBody List<Integer> ids) {
    return userService.batchDelete(ids);
}

// 后台用户信息修改
public Result<String> backUpdate(User user) {
    // 账号唯一性验证
    if (user.getUserAccount() != null) {
        boolean exists = userMapper.checkAccountExists(user.getUserAccount(), user.getId());
        if (exists) {
            return ApiResult.error("该账号已被使用，请更换其他账号");
        }
    }
    userMapper.update(user);
    return ApiResult.success();
}
```

```javascript
// 前端用户管理
export default {
  data() {
    return {
      tableData: [],           // 用户列表数据
      selectedRows: [],        // 选中的用户
      userQueryDto: {},        // 查询条件
      dialogVisible: false,    // 对话框显示状态
      editUserForm: {},        // 编辑用户表单
      // 状态选项
      loginStatuList: [
        { value: null, label: "全部" },
        { value: 0, label: "正常" },
        { value: 1, label: "封号" },
      ],
      rolesList: [
        { value: null, label: "全部" },
        { value: 2, label: "用户" },
        { value: 1, label: "管理员" },
      ]
    };
  },
  methods: {
    // 批量删除用户
    async batchDelete() {
      if (!this.selectedRows.length) {
        this.$message('未选中任何数据');
        return;
      }

      const result = await this.$swal({
        title: "删除用户数据",
        text: `确定要删除选中的 ${this.selectedRows.length} 个用户吗？`,
        icon: "warning",
        showCancelButton: true
      });

      if (result.isConfirmed) {
        let ids = this.selectedRows.map((user) => user.id);
        await this.$axios.post(`user/batchDelete`, ids);
        this.$message.success('删除成功');
        this.fetchFreshData();
      }
    },

    // 账号唯一性检查
    async checkAccountExists(account) {
      const response = await this.$axios.get(`/user/checkAccount`, {
        params: { account: account }
      });
      return response.data.data;
    }
  }
};
```

#### 4.2.6 图书借阅流程 📚

- **控制器**：`BookOrderHistoryController.java`
- **服务层**：`BookOrderHistoryService.java`、`BookOrderHistoryServiceImpl.java`
- **数据访问**：`BookOrderHistoryMapper.java`
- **前端界面**：`BookOperation.vue`（用户）、`BookOrderHistoryManage.vue`（管理员）

**功能描述**：
借阅流程涉及库存检查、借阅记录创建、图书状态更新等多个环节，通过事务确保数据一致性。用户可以在前端界面浏览可借阅的图书，选择借阅天数，系统会自动检查库存和用户借阅权限，创建借阅记录并更新图书库存。管理员可以查看所有借阅记录，处理逾期未还等异常情况。

**核心实现**：
- 借阅前库存检查，确保图书可借
- 借阅记录创建与图书库存同步更新
- 借阅期限设置与到期提醒
- 归还操作与借阅状态更新
- 借阅历史查询与统计

**关键代码**：
```java
// 文件：BookOrderHistoryServiceImpl.java 第45-78行
// 图书借阅核心逻辑
@Transactional
public Result<String> insert(BookOrderHistoryInsertDTO bookOrderHistoryInsertDTO) {
    // 1. 检查图书库存
    Book book = bookMapper.getById(bookOrderHistoryInsertDTO.getBookId());
    if (book == null) {
        return ApiResult.error("图书不存在");
    }
    if (book.getNum() <= 0) {
        return ApiResult.error("图书库存不足，无法借阅");
    }

    // 2. 检查用户借阅权限
    Integer userId = LocalThreadHolder.getUserId();
    List<BookOrderHistory> unreturnedBooks = bookOrderHistoryMapper.getUnreturnedByUserId(userId);
    if (unreturnedBooks.size() >= MAX_BORROW_COUNT) {
        return ApiResult.error("借阅数量已达上限，请先归还图书");
    }

    // 3. 创建借阅记录
    BookOrderHistory orderHistory = BookOrderHistory.builder()
        .bookId(bookOrderHistoryInsertDTO.getBookId())
        .userId(userId)
        .deadlineNum(bookOrderHistoryInsertDTO.getDeadlineNum())
        .isReturn(false)
        .createTime(LocalDateTime.now())
        .build();

    // 4. 更新图书库存（减1）
    book.setNum(book.getNum() - 1);
    bookMapper.update(book);

    // 5. 保存借阅记录
    bookOrderHistoryMapper.insert(orderHistory);

    return ApiResult.success("借阅成功");
}
```

```javascript
// 文件：BookOperation.vue 第156-189行
// 前端借阅操作
async borrowBook(book) {
  // 1. 验证借阅条件
  if (book.num <= 0) {
    this.$message.error('该图书库存不足，无法借阅');
    return;
  }

  // 2. 显示借阅确认对话框
  const result = await this.$swal({
    title: '确认借阅',
    text: `确定要借阅《${book.name}》吗？`,
    icon: 'question',
    showCancelButton: true,
    confirmButtonText: '确认借阅',
    cancelButtonText: '取消'
  });

  if (!result.isConfirmed) return;

  // 3. 发送借阅请求
  try {
    const borrowData = {
      bookId: book.id,
      deadlineNum: this.selectedDays || 30  // 默认30天
    };

    const response = await this.$axios.post('/bookOrderHistory/insert', borrowData);

    if (response.data.code === 200) {
      this.$message.success('借阅成功！');
      // 4. 更新本地图书库存显示
      book.num -= 1;
      // 5. 刷新借阅记录
      this.loadBorrowHistory();
    } else {
      this.$message.error(response.data.msg || '借阅失败');
    }
  } catch (error) {
    this.$message.error('借阅请求失败，请重试');
  }
}
```

#### 4.2.7 图书订阅通知机制 🔔

- **控制器**：`BookRssHistoryController.java`、`RssNotificationController.java`
- **服务层**：`BookRssHistoryService.java`、`RssNotificationService.java`
- **服务实现**：`BookRssHistoryServiceImpl.java`、`RssNotificationServiceImpl.java`
- **前端界面**：`BookRssHistory.vue`、`RssNotification.vue`

**功能描述**：
用户可以订阅尚未入库或已全部借出的图书，当图书入库后，系统自动向相关用户发送通知。用户可以在个人中心查看所有订阅记录和通知消息，管理员可以查看所有订阅请求，作为图书采购的参考依据。

**核心实现**：
- 图书订阅记录创建与管理
- 图书状态变更触发通知生成
- 通知消息推送与已读状态管理
- 订阅统计分析，辅助采购决策

**关键代码**：
```java
// 文件：BookRssHistoryServiceImpl.java 第52-78行
// 图书订阅核心逻辑
public Result<String> insert(BookRssHistoryInsertDTO bookRssHistoryInsertDTO) {
    // 1. 检查图书是否存在
    Book book = bookMapper.getById(bookRssHistoryInsertDTO.getBookId());
    if (book == null) {
        return ApiResult.error("图书不存在");
    }

    // 2. 检查是否已经订阅
    Integer userId = LocalThreadHolder.getUserId();
    BookRssHistory existingRss = bookRssHistoryMapper.getByUserAndBook(userId, bookRssHistoryInsertDTO.getBookId());
    if (existingRss != null) {
        return ApiResult.error("您已经订阅过该图书");
    }

    // 3. 创建订阅记录
    BookRssHistory rssHistory = BookRssHistory.builder()
        .bookId(bookRssHistoryInsertDTO.getBookId())
        .userId(userId)
        .createTime(LocalDateTime.now())
        .build();

    bookRssHistoryMapper.insert(rssHistory);
    return ApiResult.success("订阅成功，图书到货后将通知您");
}

// 文件：RssNotificationServiceImpl.java 第45-72行
// 自动通知生成逻辑
@Async
public void generateNotificationsForBook(Long bookId) {
    // 1. 查找所有订阅该图书的用户
    List<BookRssHistory> subscriptions = bookRssHistoryMapper.getByBookId(bookId);

    if (subscriptions.isEmpty()) {
        return;
    }

    // 2. 获取图书信息
    Book book = bookMapper.getById(bookId);

    // 3. 为每个订阅用户生成通知
    List<RssNotification> notifications = new ArrayList<>();
    for (BookRssHistory subscription : subscriptions) {
        RssNotification notification = RssNotification.builder()
            .userId(subscription.getUserId())
            .content(String.format("您订阅的图书《%s》已到货，现在可以借阅了！", book.getName()))
            .isRead(false)
            .createTime(LocalDateTime.now())
            .build();
        notifications.add(notification);
    }

    // 4. 批量保存通知
    rssNotificationMapper.batchInsert(notifications);
}
```

```javascript
// 文件：BookOperation.vue 第234-267行
// 前端订阅操作
async subscribeBook(book) {
  // 1. 检查订阅条件
  if (book.num > 0) {
    this.$message.info('该图书当前有库存，可以直接借阅');
    return;
  }

  // 2. 确认订阅
  const result = await this.$swal({
    title: '订阅图书',
    text: `确定要订阅《${book.name}》吗？图书到货后将通知您`,
    icon: 'question',
    showCancelButton: true,
    confirmButtonText: '确认订阅',
    cancelButtonText: '取消'
  });

  if (!result.isConfirmed) return;

  // 3. 发送订阅请求
  try {
    const subscribeData = {
      bookId: book.id
    };

    const response = await this.$axios.post('/bookRssHistory/insert', subscribeData);

    if (response.data.code === 200) {
      this.$message.success('订阅成功！图书到货后将通知您');
      // 4. 更新订阅状态显示
      book.isSubscribed = true;
      // 5. 刷新订阅记录
      this.loadSubscriptionHistory();
    } else {
      this.$message.error(response.data.msg || '订阅失败');
    }
  } catch (error) {
    this.$message.error('订阅请求失败，请重试');
  }
}
```

#### 4.2.3 个性化图书推荐系统 🧠

- **控制器**：`SimpleRecommendationController.java`
- **服务层**：`SimpleRecommendationService.java`、`SimpleRecommendationServiceImpl.java`
- **数据访问**：`BookMapper.java`、`BookOrderHistoryMapper.java`、`BookSaveMapper.java`、`BookRssHistoryMapper.java`
- **前端组件**：`UserDashboard.vue`（用户仪表盘）、`BookCard.vue`（图书卡片组件）

**功能描述**：
个性化图书推荐系统是本项目的核心特色功能之一，通过分析用户的借阅历史、收藏记录和订阅行为，结合时间衰减因子和标签均衡机制，为用户提供精准的个性化图书推荐。系统支持"换一批推荐"功能，无需刷新整页即可获取新推荐，同时保持用户滚动位置。对于新用户，默认展示热门图书，随着用户使用系统积累借阅记录，推荐会更加个性化。

**推荐策略**：
- **多维度用户行为分析**：整合借阅历史(权重0.6)、收藏记录(权重0.3)和订阅记录(权重0.1)
- **时间衰减因子**：使近期行为影响更大，采用指数衰减公式，确保推荐的时效性
- **标签均衡机制**：确保不同类型标签（"热门"、"常读"、"新书"、"作者"、"出版社"）均衡展示
- **多级推荐备选方案**：主推荐、备选方案1和备选方案2，确保推荐质量和多样性
- **推荐结果随机化**：采用分类交错策略，使不同分类的书籍交替展示，提升多样性
- **热门借阅推荐**：基于借阅量统计的热门图书，适用于新用户冷启动场景
- **新书速递**：最新入库的图书推荐，确保用户能及时了解新书信息

**前端增强组件**：
- **图书卡片**：实现3D翻转效果，包含精美上浮动画、仿真书籍效果(书脊、装订线和纸张质感)
- **标签体系**：根据推荐原因自动分配标签颜色：热门(红色)、常读(绿色)、新书(黄色)、作者(蓝色)、出版社(浅色)
- **交互体验**：鼠标悬停时封面图自动上浮，点击可翻转查看详情，书签式返回按钮
- **批量加载**：支持"换一批"功能，异步加载新推荐，无需刷新页面

**关键代码**：
```java
// 文件：SimpleRecommendationServiceImpl.java 第156-189行
// 计算用户兴趣分类权重
private Map<Long, Double> calculateUserInterests(Long userId) {
    Map<Long, Double> categoryScores = new HashMap<>();

    // 1. 借阅历史权重计算 (权重0.5)
    List<BookBehaviorVO> borrowBehaviors = orderHistoryMapper.getUserRecentBooks(userId, 10);
    if (borrowBehaviors != null) {
        for (BookBehaviorVO behavior : borrowBehaviors) {
            Long categoryId = behavior.getCategoryId();
            if (categoryId != null) {
                categoryScores.merge(categoryId, BORROW_WEIGHT, Double::sum);
            }
        }
    }

    // 2. 收藏记录权重计算 (权重0.3)
    List<BookBehaviorVO> saveBehaviors = bookSaveMapper.getUserRecentSaves(userId, 5);
    if (saveBehaviors != null) {
        for (BookBehaviorVO behavior : saveBehaviors) {
            Long categoryId = behavior.getCategoryId();
            if (categoryId != null) {
                categoryScores.merge(categoryId, SAVE_WEIGHT, Double::sum);
            }
        }
    }

    // 3. 订阅记录权重计算 (权重0.2)
    List<BookBehaviorVO> subscribeBehaviors = bookRssHistoryMapper.getUserRecentSubscriptions(userId, 5);
    if (subscribeBehaviors != null) {
        for (BookBehaviorVO behavior : subscribeBehaviors) {
            Long categoryId = behavior.getCategoryId();
            if (categoryId != null) {
                categoryScores.merge(categoryId, RSS_WEIGHT, Double::sum);
            }
        }
    }

    return categoryScores;
}
```

```javascript
// 文件：UserDashboard.vue 第298-334行
// 前端推荐加载和换一批功能
async loadRecommendations() {
  try {
    this.isLoadingRecommendations = true;

    // 1. 获取个性化推荐
    const response = await this.$axios.get('/simpleRecommendation/getRecommendations', {
      params: {
        limit: 12,  // 每次加载12本书
        offset: this.recommendationOffset
      }
    });

    if (response.data.code === 200) {
      // 2. 处理推荐数据
      const recommendations = response.data.data || [];

      // 3. 为每本书添加推荐标签和颜色
      recommendations.forEach(book => {
        book.recommendReason = this.getRecommendReason(book);
        book.tagColor = this.getTagColor(book.recommendReason);
      });

      this.recommendedBooks = recommendations;
      this.hasRecommendations = recommendations.length > 0;
    } else {
      this.$message.warning('暂无推荐图书');
      this.hasRecommendations = false;
    }
  } catch (error) {
    console.error('加载推荐失败:', error);
    this.$message.error('加载推荐失败，请重试');
    this.hasRecommendations = false;
  } finally {
    this.isLoadingRecommendations = false;
  }
},

// 换一批推荐
async changeRecommendations() {
  this.recommendationOffset += 12;
  await this.loadRecommendations();
}
```

**数据模型扩展**：
```java
// 用户分类偏好类
public class CategoryPreference {
    private Long categoryId;     // 分类ID
    private String categoryName; // 分类名称
    private Integer count;       // 行为次数
    private Date latestTime;     // 最近行为时间
}

// 标签配额管理类
class RecommendOption {
    String reason;  // 推荐原因
    String type;    // 标签类型
    int priority;   // 优先级
}
```

**性能优化**：
```sql
-- 增加索引提升查询性能
ALTER TABLE book_save ADD INDEX idx_book_save_user_id (user_id);
ALTER TABLE book_save ADD INDEX idx_book_save_book_id (book_id);
ALTER TABLE book_rss_history ADD INDEX idx_book_rss_user_id (user_id);
ALTER TABLE book_rss_history ADD INDEX idx_book_rss_book_id (book_id);
ALTER TABLE book_rss_history ADD INDEX idx_book_rss_create_time (create_time);
```

#### 4.2.4 社区化留言系统 💬

- **控制器**：`ReaderProposalController.java`、`ReaderProposalCategoryController.java`
- **服务层**：`ReaderProposalService.java`、`ReaderProposalCategoryService.java`
- **服务实现**：`ReaderProposalServiceImpl.java`、`ReaderProposalCategoryServiceImpl.java`
- **数据访问**：`ReaderProposalMapper.java`、`ReaderProposalCategoryMapper.java`、`ReaderProposalLikeMapper.java`、`ReaderProposalSaveMapper.java`
- **前端组件**：`Main.vue`（用户）、`ReaderProposal.vue`（用户）、`ReaderProposalManage.vue`（管理员）、`ReaderProposalCategoryManage.vue`（管理员）

**功能描述**：
社区留言系统是本项目的另一个特色功能，为用户提供了一个交流讨论的平台。系统支持楼中楼评论结构，实现了帖子发布、回复、点赞、收藏等功能，通过事件总线实现通知计数的跨组件更新。管理员可以对留言进行管理，设置置顶、精华和分类标签，提升社区活跃度和内容质量。

**核心功能**：
- **楼中楼回复结构**：支持二级回复层级，通过视觉设计展示回复关系，增强交互体验
- **回复高亮定位**：通过回复ID精确定位，脉冲动画引导用户注意，提升用户体验
- **帖主与管理员标识**：直观区分原帖作者和管理员回复，增强信息透明度
- **消息通知系统**：支持已读/未读状态管理，实时更新通知计数，确保用户及时获取互动信息
- **分类标签系统**：支持自定义标签名称和颜色，增强视觉区分度，方便内容分类
- **双模式管理界面**：管理员可在"只看帖子"和"用户回复通知"两种模式间切换，提高管理效率
- **批量操作功能**：支持批量删除、置顶、加精和设置标签，简化管理流程
- **点赞与收藏**：用户可以对感兴趣的留言进行点赞和收藏，增强社区互动性

**技术实现**：
- 使用Vue的事件总线(EventBus)实现组件间通信，确保通知计数实时更新
- 采用递归组件渲染楼中楼结构，支持多层级回复
- 使用动态CSS类和过渡动画增强用户体验
- 实现高效的分页加载和条件筛选，优化性能

**前端实现示例**：
```javascript
// 确保回复可见（展开父级回复的折叠区域）
ensureReplyVisible(replyId) {
  // 查找当前回复
  const reply = this.replies.find((r) => r.id === replyId);
  if (!reply) return;

  // 如果不是一级回复，需要确保其链路上的所有回复都是展开的
  if (reply.replyLevel === 2) {
    // 查找父级回复
    const primaryReply = this.replies.find((r) => r.id === reply.replyToId);
    if (primaryReply) {
      // 如果父级是一级回复，展开此回复的二级回复区
      if (primaryReply.replyLevel === 1) {
        this.$set(this.expandedReplies, primaryReply.id, true);
      }
      // 如果父级也是二级回复，递归确保其可见
      else if (primaryReply.replyLevel === 2) {
        this.ensureReplyVisible(primaryReply.id);
      }
    }
  }
}
```

**数据模型**：
```java
// 用户留言实体
public class ReaderProposal {
    private Integer id;                // 主键ID
    private Integer userId;            // 用户ID
    private String content;            // 留言内容
    private Boolean isPublish;         // 是否公开
    private String replyContent;       // 回复内容
    private Boolean isReply;           // 是否已回复
    private LocalDateTime replyTime;   // 回复时间
    private LocalDateTime createTime;  // 创建时间
    private Integer categoryId;        // 分类ID
    private Integer parentId;          // 父留言ID
    private Integer likeCount;         // 点赞数
    private Boolean isTop;             // 是否置顶
    private Boolean isEssence;         // 是否精华
    private Integer replyCount;        // 回复数量
    private Integer replyToId;         // 回复目标ID
    private Integer replyToUserId;     // 被回复用户ID
    private String replyToUserName;    // 被回复用户名
    private Integer replyLevel;        // 回复层级
    private Boolean isPostAuthor;      // 是否为原帖作者
}

// 用户留言分类
public class ReaderProposalCategory {
    private Integer id;                // 主键ID
    private String name;               // 分类名称
    private String color;              // 分类颜色
    private LocalDateTime createTime;  // 创建时间
}
```

**性能优化**：
```sql
-- 优化留言查询性能
ALTER TABLE reader_proposal ADD INDEX idx_rp_user_id (user_id);
ALTER TABLE reader_proposal ADD INDEX idx_rp_parent_id (parent_id);
ALTER TABLE reader_proposal ADD INDEX idx_rp_category_id (category_id);
ALTER TABLE reader_proposal ADD INDEX idx_rp_create_time (create_time);
ALTER TABLE reader_proposal ADD INDEX idx_rp_is_top (is_top);
ALTER TABLE reader_proposal ADD INDEX idx_rp_is_essence (is_essence);
```

#### 4.2.5 用户行为日志记录 📝

- **AOP注解**：`Log.java`
- **AOP切面**：`LogAspect.java`
- **服务层**：`UserOperationLogService.java`、`UserOperationLogServiceImpl.java`
- **数据访问**：`UserOperationLogMapper.java`
- **前端界面**：`UserOperationLog.vue`

**功能描述**：
用户行为日志系统采用AOP（面向切面编程）技术，通过注解方式对用户关键操作进行日志记录，实现用户行为的全程跟踪。系统管理员可以查看所有用户的操作日志，有助于问题排查和行为分析。

**技术实现**：
- 使用自定义注解`@Log`标记需要记录日志的方法
- 通过AOP切面拦截带有`@Log`注解的方法调用
- 自动记录操作用户、操作类型、操作内容、操作时间等信息
- 支持多维度查询和导出功能

**关键代码**：
```java
// 文件：LogAspect.java 第25-68行
// AOP日志记录切面
@Aspect
@Component
public class LogAspect {
    @Resource
    private UserOperationLogService userOperationLogService;

    @Pointcut("@annotation(cn.kmbeast.aop.Log)")
    public void logPointCut() {
    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        Object result = point.proceed();
        long time = System.currentTimeMillis() - beginTime;
        try {
            saveLog(point, time);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    private void saveLog(ProceedingJoinPoint joinPoint, long time) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        UserOperationLog operationLog = new UserOperationLog();

        // 获取注解中的操作内容
        Log log = method.getAnnotation(Log.class);
        if (log != null) {
            operationLog.setContent(log.content());
        }

        // 设置操作用户
        Integer userId = LocalThreadHolder.getUserId();
        operationLog.setUserId(userId);

        // 设置操作时间和其他信息
        operationLog.setCreateTime(LocalDateTime.now());
        operationLog.setExecuteTime(time);

        // 保存日志
        userOperationLogService.save(operationLog);
    }
}
```

```javascript
// 文件：UserOperationLog.vue 第145-178行
// 前端日志查询和管理
async fetchFreshData() {
  // 1. 构建查询参数
  const params = {
    current: this.currentPage,
    size: this.pageSize,
    userName: this.queryDto.userName,
    startTime: this.queryDto.startTime,
    endTime: this.queryDto.endTime,
    content: this.queryDto.content
  };

  try {
    // 2. 发送查询请求
    const response = await this.$axios.post('/userOperationLog/query', params);

    if (response.data.code === 200) {
      // 3. 更新表格数据
      this.tableData = response.data.data || [];
      this.totalItems = response.data.total || 0;

      // 4. 格式化显示数据
      this.tableData.forEach(log => {
        // 格式化操作时间
        if (log.createTime) {
          log.createTimeFormatted = this.$moment(log.createTime).format('YYYY-MM-DD HH:mm:ss');
        }
        // 格式化执行时间
        if (log.executeTime) {
          log.executeTimeFormatted = log.executeTime + 'ms';
        }
      });
    } else {
      this.$message.error('查询失败：' + response.data.msg);
    }
  } catch (error) {
    this.$message.error('查询请求失败，请重试');
  }
}
```

#### 4.2.6 文件上传管理 📁

- **控制器**：`FileController.java`
- **前端调用**：主要在`BookManage.vue`中使用
- **功能描述**：提供图书封面图片等文件的上传、删除功能，支持图片预览
- **存储机制**：文件存储在服务器指定目录，数据库中仅保存路径引用

**技术实现**：
- 使用SpringBoot的MultipartFile处理文件上传
- 支持文件类型和大小验证
- 自动生成唯一文件名，避免文件覆盖
- 提供文件预览和删除功能

**关键代码**：
```java
// 文件：FileController.java 第32-58行
// 文件上传核心逻辑
@PostMapping("/upload")
public Result<String> upload(@RequestParam("file") MultipartFile file) {
    if (file.isEmpty()) {
        return ApiResult.error("上传失败，请选择文件");
    }

    // 获取文件名
    String fileName = file.getOriginalFilename();
    // 生成新文件名
    String newFileName = UUID.randomUUID().toString() +
        fileName.substring(fileName.lastIndexOf("."));

    // 创建文件存储目录
    File fileDir = new File(PathUtils.getClassLoadRootPath() + "/pic");
    if (!fileDir.exists()) {
        fileDir.mkdirs();
    }

    try {
        // 保存文件
        file.transferTo(new File(fileDir.getAbsolutePath() + "/" + newFileName));
        return ApiResult.success(newFileName);
    } catch (IOException e) {
        e.printStackTrace();
        return ApiResult.error("上传失败：" + e.getMessage());
    }
}
```

```javascript
// 文件：BookManage.vue 第267-298行
// 前端文件上传处理
async handleFileUpload(file) {
  // 1. 文件类型验证
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    this.$message.error('只能上传图片文件！');
    return false;
  }

  // 2. 文件大小验证
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    this.$message.error('上传图片大小不能超过 2MB!');
    return false;
  }

  // 3. 创建FormData对象
  const formData = new FormData();
  formData.append('file', file);

  try {
    // 4. 发送上传请求
    const response = await this.$axios.post('/file/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    if (response.data.code === 200) {
      // 5. 上传成功，更新封面路径
      this.addBookForm.cover = response.data.data;
      this.$message.success('图片上传成功');
    } else {
      this.$message.error('上传失败：' + response.data.msg);
    }
  } catch (error) {
    this.$message.error('上传请求失败，请重试');
  }
}
```

#### 4.2.7 数据可视化统计 📊

- **控制器**：`ViewsController.java`
- **服务层**：`ViewsService.java`、`ViewsServiceImpl.java`
- **前端实现**：`admin/Main.vue`中使用图表组件
- **数据来源**：通过`ChartVO.java`封装统计数据
- **统计内容**：包括借阅量、用户活跃度、书籍分类分布等关键指标

**功能描述**：
数据可视化统计模块为管理员提供系统运营数据的直观展示，包括借阅量趋势、用户活跃度、图书分类分布等多维度数据分析。通过图表形式展示，帮助管理员快速了解系统运行状况，辅助决策。

**技术实现**：
- 前端使用ECharts实现多种图表展示
- 后端提供统一的数据接口，支持多种统计维度
- 支持时间范围筛选和数据导出功能
- 图表组件封装，实现代码复用

**统计维度**：
- 借阅量统计：按日/周/月统计借阅量变化趋势
- 用户活跃度：统计用户登录和操作频率
- 分类分布：统计各分类图书的借阅和收藏情况
- 热门图书：统计借阅量和收藏量最高的图书
- 系统使用情况：统计各功能模块的使用频率

## 五、关键技术方案 🛠️

### 5.1 异常处理机制

系统采用统一的异常处理机制，通过全局异常处理器捕获并处理各类异常，确保API返回格式一致，提升系统健壮性。

**实现方式**：
- 自定义异常类，区分业务异常和系统异常
- 全局异常处理器捕获并统一处理异常
- 统一响应格式，通过`Result`类封装异常信息

**核心代码**：
```java
@RestControllerAdvice
public class GlobalExceptionHandler {

    // 处理业务异常
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        return ApiResult.error(e.getCode(), e.getMessage());
    }

    // 处理参数验证异常
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> handleValidationException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        String message = bindingResult.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        return ApiResult.error(400, message);
    }

    // 处理系统异常
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return ApiResult.error("系统异常，请联系管理员");
    }
}
```

### 5.2 权限控制方案

基于角色的访问控制（RBAC），结合拦截器和AOP进行认证和授权，确保系统安全。

**实现方式**：
- JWT令牌认证，保存在请求头中
- 拦截器验证令牌有效性，提取用户信息
- 线程本地变量存储用户上下文
- 注解式权限控制，区分管理员和普通用户

**核心组件**：
- `JwtInterceptor`：JWT令牌验证拦截器
- `LocalThreadHolder`：线程本地变量，存储用户信息
- `@Protector`：权限控制注解
- `ProtectorAspect`：权限控制切面

**权限控制流程**：
1. 用户登录后获取JWT令牌
2. 请求时在请求头中携带令牌
3. 拦截器验证令牌并提取用户信息
4. 将用户信息存入线程本地变量
5. 权限控制切面检查用户角色是否有权限访问

### 5.3 数据验证策略

采用前后端结合的数据验证策略，确保数据的完整性和正确性。

**前端验证**：
- 使用Element UI表单验证规则
- 实时反馈验证结果
- 提交前进行全面验证

**后端验证**：
- 使用参数校验注解（@Valid、@NotNull等）
- AOP方式的参数保护（`@Protector`注解和`ProtectorAspect`切面）
- 业务逻辑验证，确保数据一致性

**统一响应**：
- 通过`ApiResult`和`Result`类进行响应封装
- 统一的错误码和错误信息
- 支持泛型，适应不同类型的返回数据

### 5.4 日志监控体系

基于AOP实现的用户操作日志记录系统，全面跟踪用户行为。

**技术选型**：
- 基于Spring AOP实现
- 自定义注解标记需要记录的方法
- 数据库存储日志信息

**记录内容**：
- 操作用户信息
- 操作类型和内容
- 操作时间和耗时
- 操作结果和异常信息

**查询功能**：
- 多维度查询筛选
- 时间范围查询
- 用户和操作类型筛选
- 导出和统计分析

### 5.5 分页实现方案

基于AOP实现的统一分页处理，简化分页代码，提高开发效率。

**核心组件**：
- `@Pager`注解：标记需要分页的方法
- `PagerAspect`切面：拦截分页请求，处理分页参数
- `PageResult`类：封装分页结果

**工作流程**：
1. 在Controller方法上添加`@Pager`注解
2. 切面拦截请求，从请求参数中提取分页信息
3. 设置分页参数到查询对象中
4. 执行查询，获取结果和总数
5. 封装为`PageResult`返回

**优势**：
- 减少重复代码，提高开发效率
- 统一分页逻辑，便于维护
- 支持多种查询条件组合

### 5.6 安全防护机制

基于AOP的请求保护机制，防止重复提交、参数注入等安全问题。

**核心组件**：
- `@Protector`注解：标记需要保护的方法
- `ProtectorAspect`切面：实现请求保护逻辑

**防护功能**：
- 防止重复提交：基于请求参数和用户信息生成唯一标识，使用缓存防止短时间内重复提交
- 参数验证：验证请求参数的合法性
- 权限检查：检查用户是否有权限执行操作
- 操作频率限制：限制用户操作频率，防止恶意请求

**应用范围**：
- 数据修改操作
- 敏感信息查询
- 用户认证相关接口

### 5.7 跨域处理方案

完善的跨域请求处理方案，支持前后端分离架构。

**实现方式**：
- 通过配置类和拦截器处理跨域请求
- 支持多种HTTP方法和自定义头部
- 结合权限控制，确保跨域请求的安全性

**配置示例**：
```java
@Configuration
public class CorsConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
```

### 5.8 数据库设计 💾

系统采用MySQL 8.0.34数据库，设计了完善的表结构，支持图书管理、借阅、推荐和社区互动等核心功能。

#### 5.8.1 核心表设计

- **book**：图书信息表
  - `id`：主键，自增长整数，图书表主键ID
  - `name`：书名，VARCHAR(255)，图书标题
  - `cover`：封面图片路径，VARCHAR(255)，存储图片相对路径
  - `publisher`：出版社，VARCHAR(255)，图书出版机构
  - `author`：作者，VARCHAR(255)，图书作者
  - `isbn`：ISBN编号，VARCHAR(255)，国际标准书号
  - `num`：库存数量，INT，当前可借阅数量
  - `detail`：详细描述，VARCHAR(255)，图书简介
  - `bookshelf_id`：书架ID，INT，外键关联bookshelf表
  - `category_id`：分类ID，INT，外键关联category表
  - `is_plan_buy`：是否计划购买，TINYINT(1)，标识预购图书
  - `plan_buy_time`：计划购买时间，DATE，预计入库日期
  - `create_time`：创建时间，DATETIME，记录入库时间

- **category**：图书分类表
  - `id`：主键，自增长整数，书籍类别主键ID
  - `name`：分类名称，VARCHAR(255)，类别名

- **bookshelf**：书架信息表
  - `id`：主键，自增长整数，书架表主键ID
  - `floor`：楼层，VARCHAR(255)，书架所在楼层
  - `area`：区域，VARCHAR(255)，书架所在区域
  - `frame`：架号，VARCHAR(255)，具体书架编号

- **bookshelf_category**：书架与分类关联表
  - `id`：主键，自增长整数，书架种类关联表主键
  - `bookshelf_id`：书架ID，INT，外键关联bookshelf表
  - `category_id`：分类ID，INT，外键关联category表

#### 5.8.2 借阅与推荐相关表

- **book_order_history**：图书借阅历史表
  - `id`：主键，自增长整数，图书预约历史ID
  - `book_id`：图书ID，INT，外键关联book表
  - `user_id`：用户ID，INT，外键关联user表
  - `deadline_num`：借阅数量，INT，借书的数量
  - `is_return`：是否归还，TINYINT(1)，是否已经归还
  - `return_time`：归还时间，DATE，实际归还日期
  - `create_time`：借阅时间，DATETIME，借书时间

  *该表是个性化推荐算法的核心数据来源，通过分析用户借阅历史确定用户阅读偏好*

- **book_save**：图书收藏表
  - `id`：主键，自增长整数，图书收藏主键ID
  - `book_id`：图书ID，INT，外键关联book表
  - `user_id`：用户ID，INT，外键关联user表
  - `create_time`：收藏时间，DATETIME，收藏记录创建时间

  *用于统计用户收藏情况，是推荐算法的重要参考数据*

  *已添加索引：`idx_book_save_user_id`、`idx_book_save_book_id`，提升查询性能*

#### 5.8.3 订阅与通知表

- **book_rss_history**：图书订阅历史表
  - `id`：主键，自增长整数，图书订阅ID
  - `book_id`：图书ID，INT，外键关联book表
  - `user_id`：用户ID，INT，外键关联user表
  - `create_time`：订阅时间，DATETIME，订阅记录创建时间

  *已添加索引：`idx_book_rss_user_id`、`idx_book_rss_book_id`、`idx_book_rss_create_time`，提升查询性能*

- **rss_notification**：订阅通知表
  - `id`：主键，自增长整数，订阅通知ID
  - `user_id`：用户ID，INT，外键关联user表
  - `content`：通知内容，TEXT，通知详细内容
  - `is_read`：是否已读，TINYINT(1)，用户是否已经阅读
  - `create_time`：通知时间，DATETIME，通知的时间

#### 5.8.4 社区留言相关表

- **reader_proposal**：读者留言表
  - `id`：主键，自增长整数，读者反馈ID
  - `user_id`：用户ID，INT，反馈者用户ID
  - `content`：留言内容，VARCHAR(255)，反馈的问题内容
  - `is_publish`：是否公开，TINYINT(1)，是否公开
  - `reply_content`：回复内容，VARCHAR(255)，管理员回复内容
  - `reply_time`：回复时间，DATETIME，回复时间
  - `is_reply`：是否已回复，TINYINT(1)，是否已经回复
  - `create_time`：创建时间，DATETIME，用户反馈时间
  - `category_id`：分类标签ID，BIGINT，分类标签ID
  - `parent_id`：父留言ID，BIGINT，父留言ID
  - `like_count`：点赞数，INT，点赞数，默认0
  - `is_top`：是否置顶，TINYINT(1)，是否置顶，默认0
  - `is_essence`：是否精华，TINYINT(1)，是否精华，默认0
  - `reply_count`：回复数，INT，回复数，默认0
  - `reply_to_id`：回复目标ID，INT，回复的具体评论ID
  - `reply_to_user_id`：被回复用户ID，INT，回复的用户ID
  - `reply_to_user_name`：被回复用户名称，VARCHAR(50)，回复的用户名
  - `reply_level`：回复层级，INT，回复层级(0为主评论，1为一级回复，2为二级回复)
  - `is_post_author`：是否为帖主，TINYINT(1)，是否为帖子作者的评论，默认0

  *已添加索引：`idx_category_id`、`idx_parent_id`、`idx_is_top`、`idx_is_essence`、`idx_reply_to_id`、`idx_reply_level`，提升查询性能*

- **reader_proposal_category**：留言分类表
  - `id`：主键，自增长整数，BIGINT
  - `name`：分类名称，VARCHAR(50)，标签名称
  - `color`：分类颜色，VARCHAR(20)，标签颜色，默认'info'
  - `create_time`：创建时间，DATETIME，创建时间

- **reader_proposal_like**：留言点赞关系表
  - `id`：主键，自增长整数，BIGINT
  - `proposal_id`：留言ID，BIGINT，留言ID
  - `user_id`：用户ID，BIGINT，用户ID
  - `create_time`：点赞时间，DATETIME，点赞时间

  *已添加唯一索引：`uk_proposal_user`(proposal_id, user_id)，防止重复点赞*

- **reader_proposal_save**：留言收藏关系表
  - `id`：主键，自增长整数，BIGINT
  - `proposal_id`：留言ID，BIGINT，留言ID
  - `user_id`：用户ID，BIGINT，用户ID
  - `create_time`：收藏时间，DATETIME，收藏时间

  *已添加唯一索引：`uk_proposal_user`(proposal_id, user_id)，防止重复收藏*

#### 5.8.5 用户与系统表

- **user**：用户信息表
  - `id`：主键，自增长整数，用户编号
  - `user_account`：用户账号，VARCHAR(50)，登录账号
  - `user_name`：用户昵称，VARCHAR(50)，显示名称
  - `user_pwd`：用户密码，VARCHAR(100)，加密存储
  - `user_avatar`：用户头像，VARCHAR(255)，头像图片路径
  - `user_email`：用户邮箱，VARCHAR(50)，联系邮箱
  - `user_role`：用户角色，INT，0普通用户，1管理员
  - `is_login`：可登录状态，TINYINT(1)，可登录状态(0：可用，1：不可用)
  - `is_word`：禁言状态，TINYINT(1)，禁言状态(0：可用，1：不可用)
  - `create_time`：创建时间，DATETIME，用户注册时间
  - `is_deleted`：删除状态，TINYINT(1)，删除状态(0:未删除；1：已删除)，默认0

- **user_operation_log**：用户操作日志表
  - `id`：主键，自增长整数，用户行为日志表主键ID
  - `user_id`：用户ID，INT，用户ID
  - `content`：操作内容，VARCHAR(255)，行为描述
  - `create_time`：创建时间，DATETIME，记录时间

- **notice**：系统公告表
  - `id`：主键，自增长整数，公告ID
  - `name`：公告标题，VARCHAR(255)，标题
  - `content`：公告内容，LONGTEXT，内容
  - `create_time`：创建时间，DATETIME，发布时间

通过这些表的关联和分析，系统能够实现个性化图书推荐、热门图书统计、新书通知以及社区留言互动等功能，为用户提供精准的阅读推荐服务和良好的社区交流平台。数据库设计遵循第三范式，确保数据一致性和完整性，同时通过合理的索引设计提升查询性能。


### 7.3 参考资料

| 技术 | 文档链接 | 说明 |
|------|---------|------|
| Spring Boot | [官方文档](https://spring.io/projects/spring-boot) | 后端框架 |
| Vue.js | [官方文档](https://vuejs.org/) | 前端框架 |
| Element UI | [组件文档](https://element.eleme.io/) | UI组件库 |
| MyBatis | [官方文档](https://mybatis.org/mybatis-3/) | ORM框架 |
| ECharts | [图表文档](https://echarts.apache.org/) | 数据可视化 |
| wangEditor | [编辑器文档](https://www.wangeditor.com/) | 富文本编辑器 |

### 7.4 项目特色总结

本图书管理系统具有以下特色功能和技术亮点：

1. **个性化图书推荐系统**
   - 多维度用户行为分析
   - 时间衰减因子算法
   - 标签均衡机制
   - 3D翻转图书卡片展示

2. **社区化留言系统**
   - 楼中楼评论结构
   - 点赞、收藏功能
   - 分类标签系统
   - 置顶与精华设置

3. **完善的借阅流程**
   - 全流程借阅管理
   - 到期提醒功能
   - 借阅历史记录

4. **订阅通知系统**
   - 图书到货通知
   - 订阅统计分析
   - 采购参考依据

5. **技术亮点**
   - 基于AOP的日志记录
   - 统一的异常处理
   - 注解式权限控制
   - 前后端分离架构
   - 响应式UI设计

